{"name": "sw-management", "private": true, "scripts": {"start": "/------------- START THE BUILDING COMMANDS ----------------/", "lint": "eslint .", "lint-swagger": "node ./scripts/lint-swagger.mjs", "compile": "./node_modules/.bin/lerna run compile && ./node_modules/.bin/lerna run version", "preinstall": "node ./scripts/preinstall.cjs", "generate-config": "node scripts/generate-config.mjs", "end": "/------------- END THE BUILDING COMMANDS ----------------/", "prepareRelease": "lerna version ${npm_config_lerna_version} --no-git-tag-version --force-publish --yes && node scripts/normalize-version.mjs", "compile:api": "pnpm --filter @skywind-group/sw-management-api run compile", "sync-lib": "node scripts/sync-lib.mjs", "full-clean": "pnpm run clean && pnpm i && pnpm run --if-present compile && pnpm run --if-present version", "dev:mapi": "INTERNAL_SERVER_PORT=9000 pnpm --filter @skywind-group/sw-management-api run dev:mapi", "dev:terminal": "INTERNAL_SERVER_PORT=9004 pnpm --filter @skywind-group/sw-management-api run dev:terminal", "dev:player": "INTERNAL_SERVER_PORT=9005 pnpm --filter @skywind-group/sw-management-api run dev:player", "dev:game-auth": "INTERNAL_SERVER_PORT=9007 pnpm --filter @skywind-group/sw-management-api run dev:game-auth", "dev:site": "INTERNAL_SERVER_PORT=9007 pnpm --filter @skywind-group/sw-management-api run dev:site", "dev:gameprovider-api": "INTERNAL_SERVER_PORT=9006 pnpm --filter @skywind-group/sw-management-gameprovider-api run dev", "clean:lib": "pnpm run --if-present clean", "clean": "rm -rf node_modules package-lock.json pnpm-lock.yaml packages/*/{package-lock.json,node_modules,out,lib,coverage,.nyc_output}", "test:ts:api": "pnpm --filter @skywind-group/sw-management-api run test:ts", "lint:fix": "eslint . --fix"}, "devDependencies": {"@skywind-group/sw-utils": "2.5.3", "@types/lodash": "4.17.12", "@types/node": "^22.14.1", "@types/node-schedule": "^2.1.8", "@types/superagent": "^8.1.9", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "bole": "5.0.15", "chai": "~4.3.10", "chai-as-promised": "^7.1.1", "chai-datetime": "1.8.1", "chai-shallow-deep-equal": "1.4.4", "dotenv": "^16.0.0", "eslint": "^9.24.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^58.0.0", "fast-glob": "^3.3.3", "lerna": "8.2.2", "mocha": "10.7.3", "mocha-typescript": "1.1.12", "reflect-metadata": "0.1.13", "sinon": "16.1.3", "sinon-chai": "3.7.0", "superagent": "10.2.3", "ts-node": "^10.7.0", "tsconfig-paths": "^4.2.0", "typescript": "5.6.3"}, "engines": {"node": ">=18"}, "workspaces": ["packages/*"], "packageManager": "pnpm@10.15.0"}