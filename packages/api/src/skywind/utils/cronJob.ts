import * as schedule from "node-schedule";
import { logging } from "@skywind-group/sw-utils";
import logger from "./logger";
import * as redis from "../storage/redis";

export class Lock {

    private locked: number;

    constructor(private key: string) { }

    public async lock(expire: number): Promise<boolean> {
        const client = await redis.get();
        try {
            const ts = Date.now();
            const result = await client.set(this.key, ts, "EX", expire, "NX");
            this.locked = result ? ts : undefined;
            return !!result;
        } finally {
            redis.release(client);
        }
    }

    public async unlock(): Promise<void> {
        if (!this.locked) {
            return;
        }
        const client = await redis.get();
        try {
            const ts = await client.get(this.key);
            if (+ts !== this.locked) {
                // concurrent lock
                return;
            }
            await client.del(this.key);
            this.locked = undefined;
        } finally {
            redis.release(client);
        }
    }
}

export type CronJobFn = (lastTs: number) => Promise<number | void>;

export interface CronJobConfig {
    name: string;
    schedule: string | Date;
    timeout: number;
}

export class CronJob {

    private job: schedule.Job;
    private log: logging.Logger;
    private lastTsKey: string;
    private lock: Lock;

    constructor(private config: CronJobConfig, private jobFn: CronJobFn) {
        this.log = logger(`job:${config.name}`);
        this.lastTsKey = `job:${config.name}:lastTs`;
        this.lock = new Lock(`job:${config.name}:lock`);

        this.job = schedule.scheduleJob(config.schedule, async () => {
            await this.invoke();
        });
    }

    public async invoke(): Promise<void> {
        const locked = await this.lock.lock(this.config.timeout);
        if (!locked) {
            return;
        }

        const lastTs = await this.getLastTs(this.lastTsKey);

        this.log.info("Execute job %s. Last date: %s", this.config.name, new Date(lastTs).toISOString());
        this.log.info("Next job invocation: %s", this.job.nextInvocation());

        try {
            const ts = await this.jobFn(lastTs);
            await this.updateLastTs(this.lastTsKey, ts || Date.now());
        } catch (err) {
            this.log.error(err, "Failed to execute job %s", this.config.name);
        } finally {
            await this.lock.unlock();
        }
    }

    public cancel() {
        if (this.job) {
            this.job.cancel();
        }
    }

    private async getLastTs(key: string): Promise<number> {
        const client = await redis.get();
        try {
            const ts = await client.get(key);
            return ts ? +ts : undefined;
        } finally {
            redis.release(client);
        }
    }

    private async updateLastTs(key: string, ts: number) {
        const client = await redis.get();
        try {
            const lastTs = await client.get(key);
            if (lastTs && +lastTs >= ts) {
                return;
            }
            await client.set(key, ts);
        } finally {
            redis.release(client);
        }
    }
}
