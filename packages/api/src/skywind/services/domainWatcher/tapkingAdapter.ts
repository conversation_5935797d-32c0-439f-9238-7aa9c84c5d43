import { logging } from "@skywind-group/sw-utils";
import config from "../../config";
import { HttpClient, HttpClientConfig, HttpClientError, createHttpClient } from "../../utils/httpClient";
import { DomainWatcherAdapter, DomainStatus, MonitoredDomain } from "./types";

export type HttpMethod = "GET" | "POST" | "HEAD";
export type HttpSchema = "HTTP" | "HTTPS";
export type BodyPredicate = "EQUALS" | "CONTAINS";

export interface ValidityRuleCode {
    from: number;
    to: number;
}

export interface ValidityRuleBody {
    predicate: BodyPredicate;
    content: string;
    ignoreCase?: boolean;
}

export interface ValidityRule {
    code: ValidityRuleCode;
    body?: ValidityRuleBody | null;
}

export interface CheckEndpoint {
    uri?: string | null;
    schema?: HttpSchema;
    headers?: Record<string, string[]>;
    method?: HttpMethod;
    body?: string | null;
    validityRule?: ValidityRule;
}

export interface Region {
    countryCode: string;
}

export interface DomainRegistrationRequest {
    domain: string;
    checkEndpoint?: CheckEndpoint;
    regions?: Region[];
}

export interface TapkingDomainStatus extends DomainStatus {
    region: Region;
    checkId?: string | null;
}

export interface TapkingMonitoredDomain {
    clientId?: string;
    name: string;
    createdAt?: number;
    checkEndpoint?: CheckEndpoint;
    statuses: TapkingDomainStatus[];
}

function mapTo({ name, statuses }: TapkingMonitoredDomain): MonitoredDomain {
    let status = statuses.find(({ accessStatus }) => accessStatus === "BLOCKED");
    if (!status) {
        status = statuses.sort((a, b) => new Date(a.lastCheckedAt || 0).getTime() - new Date(b.lastCheckedAt || 0).getTime())[0];
    }
    return {
        domain: name,
        status: status ?? { accessStatus: "UNKNOWN" }
    };
}

export class TapkingAdapter implements DomainWatcherAdapter {
    public readonly adapterId = "tapking";
    private readonly httpClient: HttpClient;

    constructor({ token: bearerToken, baseUrl, ...config }: HttpClientConfig & { token: string }, log?: logging.Logger) {
        this.httpClient = createHttpClient({
            ...config,
            baseUrl: `${baseUrl?.replace(/\/$/, "") ?? ""}/api/v1/domains`,
            headers: {
                "Authorization": `Bearer ${bearerToken}`,
                "Content-Type": "application/json"
            },
            wrapError: (err) => {
                if (err instanceof HttpClientError) {
                    throw new DomainValidatorError(err.message, err.statusCode, err.responseBody);
                }
                throw err;
            }
        }, log);
    }

    public async register(domain: string) {
        const data = await this.httpClient.post<TapkingMonitoredDomain>("", { domain });
        return mapTo(data);
    }

    public async list() {
        const data = await this.httpClient.get<TapkingMonitoredDomain[]>("");
        return data.map(mapTo);
    }

    public async get(domain: string) {
        const data = await this.httpClient.get<TapkingMonitoredDomain>(`/${encodeURIComponent(domain)}`);
        return mapTo(data);
    }

    public async remove(domain: string) {
        await this.httpClient.delete(`/${encodeURIComponent(domain)}`);
    }
}

export class DomainValidatorError extends Error {
    public readonly statusCode?: number;
    public readonly responseBody?: any;

    constructor(message: string, statusCode?: number, responseBody?: any) {
        super(message);
        this.name = "DomainValidatorError";
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }
}

export function getTapkingAdapter(log?: logging.Logger) {
    const { tapking: { baseUrl, token }, timeout, retryDelay, retryAttempts, keepAlive } = config.domainMonitoring.adapters;
    return new TapkingAdapter({
        baseUrl,
        token,
        timeout,
        retryConfig: {
            sleep: retryDelay,
            maxTimeout: retryAttempts * retryDelay
        },
        keepAlive
    }, log);
}
