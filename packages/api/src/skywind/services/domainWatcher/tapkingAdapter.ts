import { logging } from "@skywind-group/sw-utils";
import config from "../../config";
import { HttpClient, HttpClientConfig, HttpClientError, createHttpClient } from "../../utils/httpClient";
import { DomainWatcherAdapter, DomainStatus, MonitoredDomain, Region } from "./types";

export interface TapkingMonitoredDomain {
    name: string;
    statuses: DomainStatus[];
}

function mapTo({ name, statuses }: TapkingMonitoredDomain): MonitoredDomain {
    let status = statuses.find(({ accessStatus }) => accessStatus === "BLOCKED");
    if (!status) {
        status = statuses.sort((a, b) => new Date(a.lastCheckedAt || 0).getTime() - new Date(b.lastCheckedAt || 0).getTime())[0];
    }
    return {
        domain: name,
        status: status ?? { accessStatus: "UNKNOWN" }
    };
}

export class TapkingAdapter implements DomainWatcherAdapter {
    public readonly adapterId = "tapking";
    private readonly httpClient: HttpClient;
    private readonly defaultRegions?: Region[];

    constructor({ token: bearerToken, baseUrl, regions, ...config }: HttpClientConfig & { token: string; regions?: Region[] }, log?: logging.Logger) {
        this.defaultRegions = regions;
        this.httpClient = createHttpClient({
            ...config,
            baseUrl: `${baseUrl?.replace(/\/$/, "") ?? ""}/api/v1/domains`,
            headers: {
                "Authorization": `Bearer ${bearerToken}`,
                "Content-Type": "application/json"
            },
            wrapError: (err) => {
                if (err instanceof HttpClientError) {
                    throw new DomainValidatorError(err.message, err.statusCode, err.responseBody);
                }
                throw err;
            }
        }, log);
    }

    public async register(domain: string) {
        const requestBody: { domain: string; regions?: Region[] } = { domain };
        if (this.defaultRegions && this.defaultRegions.length > 0) {
            requestBody.regions = this.defaultRegions;
        }
        const data = await this.httpClient.post<TapkingMonitoredDomain>("", requestBody);
        return mapTo(data);
    }

    public async list() {
        const data = await this.httpClient.get<TapkingMonitoredDomain[]>("");
        return data.map(mapTo);
    }

    public async get(domain: string) {
        const data = await this.httpClient.get<TapkingMonitoredDomain>(`/${encodeURIComponent(domain)}`);
        return mapTo(data);
    }

    public async remove(domain: string) {
        await this.httpClient.delete(`/${encodeURIComponent(domain)}`);
    }
}

export class DomainValidatorError extends Error {
    public readonly statusCode?: number;
    public readonly responseBody?: any;

    constructor(message: string, statusCode?: number, responseBody?: any) {
        super(message);
        this.name = "DomainValidatorError";
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }
}

export function getTapkingAdapter(log?: logging.Logger) {
    const { tapking: { baseUrl, token, regions }, timeout, retryDelay, retryAttempts, keepAlive } = config.domainMonitoring.adapters;
    return new TapkingAdapter({
        baseUrl,
        token,
        regions,
        timeout,
        retryConfig: {
            sleep: retryDelay,
            maxTimeout: retryAttempts * retryDelay
        },
        keepAlive
    }, log);
}
