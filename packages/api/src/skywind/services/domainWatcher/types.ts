import { DomainSource } from "./domainSource";

export type AdapterDomains = Map<string, DomainSources>;
export type DomainSources = Map<string, { domainId: number; sources: DomainSource[] }>;

export type AccessStatus = "AVAILABLE" | "BLOCKED" | "UNKNOWN";

export interface DomainStatus {
    accessStatus: AccessStatus;
    lastCheckedAt?: string | null;
}

export interface MonitoredDomain {
    domain: string;
    status: DomainStatus;
}

export interface Region {
    countryCode: string;
}

export interface DomainWatcherAdapter {
    adapterId: string;
    register(domain: string, regions?: Region[]): Promise<MonitoredDomain>;
    remove(domain: string): Promise<void>;
    get(domain: string): Promise<MonitoredDomain>;
    list(): Promise<MonitoredDomain[]>;
}
