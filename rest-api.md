## REST API

### Authentication

All API endpoints require authentication using the <PERSON><PERSON> token in the Authorization header.

Example:
```
Authorization: <PERSON><PERSON> eyJhbGciOiJ****nR5cCI6IkpXVCJ9
```

### API Endpoints

#### Domain Management

##### Register a new domain for monitoring

```http request
POST https://api-prd--shared.adm7prd.com/domain-block-detector__rest/api/v1/domains
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJ****nR5cCI6IkpXVCJ9

{
  "domain": "api-baywin-tr--prd--pl-sb.apin18erdg.com",
  "checkEndpoint": {
    "uri": "/time",
    "headers": {
      "content-Type": [
        "application/json"
      ]
    },
    "method": "GET",
    "validityRule": {
      "code": {
        "from": 200,
        "to": 299
      }
    }
  },
  "regions": [
    {
      "countryCode": "TR"
    }
  ]
}
```

```json5
{
  // required, uniq value per client
  "domain": "api-baywin-tr--prd--pl-sb.apin18erdg.com",
  // optional, if not provided default value will be:
  // {
  //   "uri": null,
  //   "schema": "HTTPS",
  //   "headers": {}, 
  //   "method": "GET",
  //   "validityRule": {
  //      "code": {
  //        "from": 200,
  //        "to": 299
  //      },
  //      "body": null
  //   }
  // } 
  //
  //
  "checkEndpoint": {
    "uri": "/time",
    // URL will be: `$schema://$domainName[/$uri]`; optional, default: null

    "schema": "https",
    // enum {HTTP, HTTPS};                          optional, default: "https"

    "headers": {
      // type: Map<String, List<String>>;             optional, default empty map.
      "content-Type": [
        "application/json"
      ]
    },
    "method": "GET",
    // enum {GET, POST, HEAD};                      optional, default GET.

    // optional, if not provided default value will be:
    // {
    //   "code": {
    //      "form": 200,
    //      "to": 299
    //   },
    //   body: null
    // }
    "validityRule": {
      "code": {
        "from": 200,
        "to": 299
      },
      // optional, if not provided default value will `null` -- response body will not be validated.
      body: {
        "predicate": "EQUALS",
        // enum {EQUALS, CONTAINS};      required.
        "content": "<SOME CONTENT>",
        // content to match;             required.
        "ignoreCase": true
        // type: Boolean;                optional, default true. 
      }
    }
  },
  // List<Region>; when empty client default region will be used, when client doesn't have default region 
  // should have at least one Region.
  "regions": [
    {
      // enum {TR,BR};                   required.
      "countryCode": "TR"
    }
  ]
}
```

**Response** (201 Created)
```json
{
  "clientId": "client123",
  "name": "example.com",
  "createdAt": 1751683700000,
  "checkEndpoint": {
    "uri": null,
    "schema": "HTTPS",
    "headers": {},
    "method": "HEAD",
    "body": null,
    "validityRule": {
      "code": {
        "from": 200,
        "to": 299
      },
      "body": null
    }
  },
  "statuses": [
    {
      "region": {
        "countryCode": "TR"
      },
      "accessStatus": "UNKNOWN",
      "lastCheckedAt": null,
      "checkId": null
    }
  ]
}
```

##### List all domains for an authenticated client

```http request
GET https://api-prd--shared.adm7prd.com/domain-block-detector__rest/api/v1/domains
Authorization: Bearer eyJhbGciOiJ****nR5cCI6IkpXVCJ9
```

**Response** (200 OK)
```json5
[
  {
    // client name
    "client": "tapking",
    // domain
    "name": "samiz709w-p.imi7n3.link",
    "checkEndpoint": {
      "uri": null,
      "schema": "HTTPS",
      "headers": {},
      "method": "HEAD",
      "body": null,
      "validityRule": {
        "code": {
          "from": 200,
          "to": 299
        },
        "body": null
      }
    },
    // when domain was added
    "createdAt": "1754505038973",
    // statuses in regions
    "statuses": [
      {
        "region": {
          "countryCode": "TR"
        },
        // [!] Most important filed. 
        // Status may have 3 states:
        // AVAILABLE - domain are available in region
        // BLOCKED - domain are blocked in region
        // UNKNOWN - system can't determinate status of domain (in this case status should be checked manully) 
        "accessStatus": "AVAILABLE",
        "lastCheckedAt": "1754505324058",
        "checkId": "019880aa-9ed5-7960-bb7b-bbcb9a29dda1"
      }
    ]
  }
]
```

##### Get specific domain information with status

```http request
GET https://api-prd--shared.adm7prd.com/domain-block-detector__rest/api/v1/domains/{domainName}
Authorization: Bearer eyJhbGciOiJ****nR5cCI6IkpXVCJ9
```

**Response** (200 OK)
```json
{
  "clientId": "client123",
  "name": "example.com",
  "createdAt": 1751683700000,
  "checkEndpoint": {
    "uri": null,
    "schema": "HTTPS",
    "headers": {},
    "method": "HEAD",
    "body": null,
    "validityRule": {
      "code": {
        "from": 200,
        "to": 299
      },
      "body": null
    }
  },
  "statuses": [
    {
      "region": {
        "countryCode": "TR"
      },
      "accessStatus": "AVAILABLE",
      "lastCheckedAt": 1751683512000,
      "checkId": "019880a8-a0c2-7ce7-abc5-3782e0c9c198"
    },
    {
      "region": {
        "countryCode": "BR"
      },
      "accessStatus": "BLOCKED",
      "lastCheckedAt": 1751683582000,
      "checkId": "019880a8-a0c2-7ce7-abc5-3782e0c9c199"
    }
  ]
}
```

##### Remove a domain from monitoring

```http request
DELETE /api/v1/domains/{domainName}
Authorization: Bearer eyJhbGciOiJ****nR5cCI6IkpXVCJ9
```

**Response** (204 No Content)
